import { CIRCLE_TYPES, CATEGORY_TYPES } from '../constants/dunbarCircles';

/**
 * Contact data model
 */
export class Contact {
  constructor({
    id = null,
    name = '',
    phone = null,
    email = null,
    circle = 'full',
    categories = [],
    lastInteracted = null,
    recurringReminder = null,
    notes = '',
    createdAt = new Date(),
    updatedAt = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.name = name;
    this.phone = phone;
    this.email = email;
    this.circle = CIRCLE_TYPES.includes(circle) ? circle : 'full';
    this.categories = this.validateCategories(categories);
    this.lastInteracted = lastInteracted ? new Date(lastInteracted) : null;
    this.recurringReminder = recurringReminder; // ReminderRule object
    this.notes = notes;
    this.createdAt = new Date(createdAt);
    this.updatedAt = new Date(updatedAt);
  }

  validateCategories(categories) {
    if (!Array.isArray(categories)) return [];
    return categories.filter(category => CATEGORY_TYPES.includes(category));
  }

  generateId() {
    return 'contact_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      phone: this.phone,
      email: this.email,
      circle: this.circle,
      categories: this.categories,
      lastInteracted: this.lastInteracted?.toISOString(),
      recurringReminder: this.recurringReminder?.toJSON(),
      notes: this.notes,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString()
    };
  }

  static fromJSON(data) {
    const contact = new Contact(data);
    // Handle recurringReminder deserialization
    if (data.recurringReminder) {
      const { ReminderRule } = require('./dataModels');
      contact.recurringReminder = ReminderRule.fromJSON(data.recurringReminder);
    }
    return contact;
  }
}

/**
 * Interaction data model
 */
export class Interaction {
  constructor({
    id = null,
    contactId = '',
    type = 'other',
    date = new Date(),
    duration = null, // in minutes for calls/meetings
    quality = 'good', // 'brief', 'good', 'deep'
    mood = 'positive', // 'positive', 'neutral', 'challenging'
    notes = '',
    createdAt = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.contactId = contactId;
    this.type = ['call', 'text', 'meeting', 'other'].includes(type) ? type : 'other';
    this.date = new Date(date);
    this.duration = duration && duration > 0 ? duration : null;
    this.quality = ['brief', 'good', 'deep'].includes(quality) ? quality : 'good';
    this.mood = ['positive', 'neutral', 'challenging'].includes(mood) ? mood : 'positive';
    this.notes = notes;
    this.createdAt = new Date(createdAt);
  }

  generateId() {
    return 'interaction_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      type: this.type,
      date: this.date.toISOString(),
      duration: this.duration,
      quality: this.quality,
      mood: this.mood,
      notes: this.notes,
      createdAt: this.createdAt.toISOString()
    };
  }

  static fromJSON(data) {
    return new Interaction(data);
  }
}

/**
 * Reminder Rule data model (stored within contact)
 * Defines recurring reminder frequency for a contact
 */
export class ReminderRule {
  constructor({
    id = null,
    contactId = '',
    type = 'recurring',
    frequency = 30, // Days between reminders
    updatedDate = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.contactId = contactId;
    this.type = 'recurring';
    this.frequency = frequency > 0 ? frequency : 30;
    this.updatedDate = new Date(updatedDate);
  }

  generateId() {
    return 'rule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      type: this.type,
      frequency: this.frequency,
      updatedDate: this.updatedDate.toISOString()
    };
  }

  static fromJSON(data) {
    return new ReminderRule(data);
  }
}

/**
 * Calculated Reminder data model (not stored, generated on-demand)
 * Represents reminder information calculated from contacts and interactions
 */
export class CalculatedReminder {
  constructor({
    id = null,
    contactId = '',
    contact = null,
    type = 'recurring', // 'recurring' or 'one-off'
    dueDate = new Date(),
    snoozedUntilDate = null,
    isOverdue = false,
    isUpcoming = false,
    daysSinceLastContact = 0,
    frequency = 30
  } = {}) {
    this.id = id || this.generateId(contactId, dueDate);
    this.contactId = contactId;
    this.contact = contact;
    this.type = type;
    this.dueDate = new Date(dueDate);
    this.snoozedUntilDate = snoozedUntilDate ? new Date(snoozedUntilDate) : null;
    this.isOverdue = isOverdue;
    this.isUpcoming = isUpcoming;
    this.daysSinceLastContact = daysSinceLastContact;
    this.frequency = frequency;
  }

  generateId(contactId, dueDate) {
    // Create deterministic ID based on due date and contact ID
    const dueDateTimestamp = dueDate.getTime();
    return `reminder_${dueDateTimestamp}_${contactId}`;
  }

  // Helper methods
  get isSnoozed() {
    return this.snoozedUntilDate !== null && this.snoozedUntilDate > new Date();
  }

  get isActive() {
    return !this.isSnoozed && (this.isOverdue || this.isUpcoming);
  }

  // For compatibility with existing UI components
  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      contact: this.contact,
      type: this.type,
      dueDate: this.dueDate.toISOString(),
      snoozedUntilDate: this.snoozedUntilDate?.toISOString(),
      isOverdue: this.isOverdue,
      isUpcoming: this.isUpcoming,
      daysSinceLastContact: this.daysSinceLastContact,
      frequency: this.frequency
    };
  }
}


