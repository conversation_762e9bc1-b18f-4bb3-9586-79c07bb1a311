import React, { useState, useMemo } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import {
  Appbar,
  Searchbar,
  List,
  Avatar,
  Button,
  Text,
  Chip,
  FAB,
  Surface,
  IconButton,
  useTheme
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { DUNBAR_CIRCLES, CONTACT_CATEGORIES, CIRCLE_TYPES } from '../constants/dunbarCircles';

export default function ContactsScreen({ navigation }) {
  const { state } = useApp();
  const theme = useTheme();

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCircleFilter, setSelectedCircleFilter] = useState(0); // 0 = All, 1-4 = specific circles

  // Circle filter options
  const circleFilterOptions = ['All', ...Object.values(DUNBAR_CIRCLES).map(circle => circle.name)];

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Render circle filter chips
  const renderCircleFilters = () => (
    <Surface style={styles.filtersContainer} elevation={1}>
      <FlatList
        data={circleFilterOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item, index }) => {
          const isSelected = selectedCircleFilter === index;
          const circleKey = index > 0 ? CIRCLE_TYPES[index - 1] : null;
          const count = circleKey ? circleStats[circleKey] : state.contacts.length;
          const circleColor = circleKey ? DUNBAR_CIRCLES[circleKey].color : '#45B7D1';

          return (
            <Chip
              mode={isSelected ? 'flat' : 'outlined'}
              selected={isSelected}
              onPress={() => setSelectedCircleFilter(index)}
              style={[
                styles.filterChip,
                isSelected && { backgroundColor: circleColor + '20' }
              ]}
              textStyle={[
                styles.filterChipText,
                isSelected && { color: circleColor, fontWeight: 'bold' }
              ]}
            >
              {item} ({count})
            </Chip>
          );
        }}
      />
    </Surface>
  );

  // Filtered and sorted contacts
  const filteredContacts = useMemo(() => {
    let filtered = state.contacts;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(contact => {
        const nameMatch = contact.name.toLowerCase().includes(query);
        const phoneMatch = contact.phone?.toLowerCase().includes(query);
        const emailMatch = contact.email?.toLowerCase().includes(query);
        const categoryMatch = contact.categories?.some(cat =>
          CONTACT_CATEGORIES[cat]?.name.toLowerCase().includes(query)
        );
        const notesMatch = contact.notes?.toLowerCase().includes(query);

        return nameMatch || phoneMatch || emailMatch || categoryMatch || notesMatch;
      });
    }

    // Filter by circle
    if (selectedCircleFilter > 0) {
      const selectedCircle = CIRCLE_TYPES[selectedCircleFilter - 1];
      filtered = filtered.filter(contact => contact.circle === selectedCircle);
    }

    // Sort by name
    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }, [state.contacts, searchQuery, selectedCircleFilter]);

  // Get contact counts per circle
  const circleStats = useMemo(() => {
    const stats = {};
    CIRCLE_TYPES.forEach(circle => {
      stats[circle] = state.contacts.filter(contact => contact.circle === circle).length;
    });
    return stats;
  }, [state.contacts]);

  const renderContact = ({ item: contact }) => {
    const circle = DUNBAR_CIRCLES[contact.circle];
    const categoryNames = contact.categories?.map(cat => CONTACT_CATEGORIES[cat]?.name).join(', ') || '';
    const lastInteractionText = contact.lastInteraction
      ? `Last contact: ${new Date(contact.lastInteraction).toLocaleDateString()}`
      : 'No interactions yet';

    return (
      <List.Item
          title={contact.name}
          description={() => (
            <View style={styles.contactInfo}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text variant="bodySmall" style={[styles.circleText, { color: circle.color }]}>
                  {circle.name}
                </Text>
                {categoryNames ? (
                  <Text variant="bodySmall" style={[styles.categoryText, { marginLeft: 8 }]}>
                    🏷️ {categoryNames}
                  </Text>
                ) : null}
              </View>
              <Text variant="bodySmall" style={styles.lastInteractionText}>
                {lastInteractionText}
              </Text>
            </View>
          )}
          left={() => (
            <Avatar.Text
              size={48}
              label={contact.name.charAt(0).toUpperCase()}
              style={{ backgroundColor: circle.color }}
            />
          )}
          right={() => <List.Icon icon="chevron-right" />}
          onPress={() => navigation.navigate('ContactDetail', { contact: contact.toJSON() })}
          titleStyle={styles.contactName}
          style={styles.contactListItem}
        />
    );
  };

  const renderEmptyState = () => {
    const isSearching = searchQuery.trim() || selectedCircleFilter > 0;

    if (isSearching) {
      return (
        <View style={styles.emptyState}>
          <IconButton
            icon="magnify-close"
            size={64}
            iconColor="#ccc"
          />
          <Text variant="headlineSmall" style={styles.emptyTitle}>
            No Results Found
          </Text>
          <Text variant="bodyLarge" style={styles.emptySubtitle}>
            {searchQuery.trim()
              ? `No contacts match "${searchQuery}"`
              : `No contacts in ${circleFilterOptions[selectedCircleFilter]}`
            }
          </Text>
          <Button
            mode="outlined"
            onPress={() => {
              setSearchQuery('');
              setSelectedCircleFilter(0);
            }}
            style={styles.clearButton}
          >
            Clear Filters
          </Button>
        </View>
      );
    }

    return (
      <View style={styles.emptyState}>
        <IconButton
          icon="account-plus"
          size={64}
          iconColor="#ccc"
        />
        <Text variant="headlineSmall" style={styles.emptyTitle}>
          No Contacts Yet
        </Text>
        <Text variant="bodyLarge" style={styles.emptySubtitle}>
          Start building your relationship network by adding your first contact
        </Text>
        <Button
          mode="contained"
          onPress={() => navigation.navigate('AddContact')}
          style={styles.addButton}
        >
          Add Contact
        </Button>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title={`Contacts (${filteredContacts.length})`} />
        <Appbar.Action
          icon="plus"
          onPress={() => navigation.navigate('AddContact')}
        />
      </Appbar.Header>

      {state.contacts.length > 0 && (
        <>
          <Searchbar
            placeholder="Search contacts..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
          {renderCircleFilters()}
        </>
      )}
      {filteredContacts.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={filteredContacts}
          renderItem={renderContact}
          keyExtractor={(item) => item.id}
          style={styles.list}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      )}
      {state.contacts.length > 0 && (
        <FAB
          icon="plus"
          style={styles.fab}
          onPress={() => navigation.navigate('AddContact')}
        />
      )}
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  searchBar: {
    margin: 16,
    elevation: 2,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  filterChipText: {
    fontSize: 12,
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 80, // Space for FAB
  },
  contactListItem: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outline,
  },
  contactName: {
    fontWeight: 'bold',
  },
  contactInfo: {
    marginTop: 4,
    gap: 2,
  },
  circleText: {
    fontWeight: '600',
    marginBottom: 2,
  },
  contactDetail: {
    opacity: 0.8,
    marginBottom: 1,
  },
  categoryText: {
    opacity: 0.7,
    marginBottom: 1,
  },
  lastInteractionText: {
    opacity: 0.6,
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  emptySubtitle: {
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
    lineHeight: 24,
  },
  addButton: {
    marginTop: 8,
  },
  clearButton: {
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
