import React, { useState } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { 
  Card, 
  Text, 
  Button, 
  Chip,
  Surface,
  useTheme 
} from 'react-native-paper';
import { DUNBAR_CIRCLES, CONTACT_CATEGORIES } from '../constants/dunbarCircles';

export default function ReminderCard({ 
  reminder, 
  onSnooze, 
  onLog, 
  onRemove,
  style 
}) {
  const theme = useTheme();
  const [fadeAnim] = useState(new Animated.Value(1));
  const [scaleAnim] = useState(new Animated.Value(1));

  const { contact } = reminder;
  if (!contact) return null;

  const circle = DUNBAR_CIRCLES[contact.circle];
  const isOverdue = reminder.isOverdue;

  // Get formatted due date
  const getDueDateText = () => {
    const dueDate = new Date(reminder.dueDate);
    const now = new Date();
    const daysDiff = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

    if (isOverdue) {
      const overdueDays = Math.abs(daysDiff);
      return overdueDays === 1 ? '1 day overdue' : `${overdueDays} days overdue`;
    } else {
      if (daysDiff === 0) return 'Due today';
      if (daysDiff === 1) return 'Due tomorrow';
      return `Due in ${daysDiff} days`;
    }
  };

  // Get contact categories for display
  const getContactTags = () => {
    if (!contact.categories || contact.categories.length === 0) return [];
    return contact.categories.map(categoryKey => CONTACT_CATEGORIES[categoryKey]).filter(Boolean);
  };

  // Handle card removal with animation
  const handleRemove = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onRemove) onRemove();
    });
  };

  // Handle snooze action
  const handleSnooze = () => {
    if (onSnooze) {
      onSnooze(reminder.id);
      handleRemove();
    }
  };

  // Handle log action
  const handleLog = () => {
    if (onLog) {
      onLog(contact);
      // Don't remove the reminder here - let it be removed when user actually logs an interaction
    }
  };

  const tags = getContactTags();

  return (
    <Animated.View 
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      <Card 
        style={[
          styles.card,
          isOverdue && styles.overdueCard
        ]}
        elevation={2}
      >
        <Card.Content>
          {/* Header with contact name and due date */}
          <View style={styles.header}>
            <Text variant="titleLarge" style={styles.contactName}>
              {contact.name}
            </Text>
            <Text 
              variant="bodySmall" 
              style={[
                styles.dueDate,
                isOverdue && styles.overdueDueDate
              ]}
            >
              {getDueDateText()}
            </Text>
          </View>

          {/* Circle and tags */}
          <View style={styles.metaContainer}>
            <View style={styles.circleContainer}>
              <Surface 
                style={[
                  styles.circleIndicator, 
                  { backgroundColor: circle.color }
                ]} 
                elevation={1}
              />
              <Text variant="bodyMedium" style={styles.circleName}>
                {circle.name}
              </Text>
            </View>

            {tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <Chip
                    key={index}
                    mode="outlined"
                    compact
                    icon={tag.icon}
                    style={[
                      styles.tag,
                      { borderColor: tag.color }
                    ]}
                    textStyle={[
                      styles.tagText,
                      { color: tag.color }
                    ]}
                  >
                    {tag.name}
                  </Chip>
                ))}
              </View>
            )}
          </View>



          {/* Action buttons */}
          <View style={styles.actionsContainer}>
            <Button
              mode="outlined"
              onPress={handleSnooze}
              icon="clock-outline"
              style={styles.actionButton}
              contentStyle={styles.actionButtonContent}
            >
              Snooze
            </Button>
            
            <Button
              mode="contained"
              onPress={handleLog}
              icon="pencil"
              style={styles.actionButton}
              contentStyle={styles.actionButtonContent}
            >
              Log Interaction
            </Button>
          </View>
        </Card.Content>
      </Card>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginVertical: 6,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B6B',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  contactName: {
    flex: 1,
    fontWeight: 'bold',
    marginRight: 12,
  },
  dueDate: {
    opacity: 0.7,
  },
  overdueDueDate: {
    color: '#FF6B6B',
    fontWeight: '600',
    opacity: 1,
  },
  metaContainer: {
    marginBottom: 12,
  },
  circleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  circleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  circleName: {
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    height: 28,
  },
  tagText: {
    fontSize: 12,
  },
  lastInteraction: {
    opacity: 0.6,
    fontStyle: 'italic',
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  actionButtonContent: {
    paddingVertical: 4,
  },
});
