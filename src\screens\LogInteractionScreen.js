import React from 'react';
import { useApp } from '../context/AppContext';
import { Interaction } from '../utils/dataModels';
import InteractionForm from '../components/InteractionForm';

export default function LogInteractionScreen({ route, navigation }) {
  const { contact: contactParam } = route.params;
  const { actions, state } = useApp();

  // Get the actual Contact instance from state (contactParam might be serialized JSON)
  const contact = state.contacts.find(c => c.id === contactParam.id) || contactParam;

  const handleSubmit = async (formData) => {
    const interaction = new Interaction({
      contactId: contact.id,
      type: formData.type,
      date: formData.date,
      duration: formData.duration,
      quality: formData.quality,
      mood: formData.mood,
      notes: formData.notes
    });

    await actions.addInteraction(interaction);
    return { message: 'Interaction logged successfully!' };
  };

  return (
    <InteractionForm
      contact={contact}
      onSubmit={handleSubmit}
      submitButtonText="Log Interaction"
      headerTitle="Log Interaction"
      contactSubtitle="Log your interaction with this contact"
      navigation={navigation}
    />
  );
}
