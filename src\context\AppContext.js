import React, { createContext, useContext, useReducer, useEffect } from 'react';
import dataService from '../services/dataService';
import snoozeDataService from '../services/snoozeDataService';
import testDataService from '../services/testDataService';
import { Contact, Interaction, ReminderRule } from '../utils/dataModels';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';
import { REMINDER_CONFIG } from '../constants/reminderConfig';
import {
  generateInitialReminderInstance,
  generateNextReminderInstance,
  syncContactReminderInstances,
  handleInteractionLogged,
  cleanupContactReminderInstances,
  completeReminderInstance,
  generateMissingReminderInstances
} from '../services/reminderInstanceService';
import {
  calculateActiveReminders,
  calculateHomePageReminders,
  calculateDisplayReminders,
  getContactReminder,
  isContactOverdue,
  calculateNextReminderDate
} from '../services/reminderCalculationService';

// Action types
export const ACTION_TYPES = {
  // Loading states
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',

  // Contacts
  SET_CONTACTS: 'SET_CONTACTS',
  ADD_CONTACT: 'ADD_CONTACT',
  UPDATE_CONTACT: 'UPDATE_CONTACT',
  DELETE_CONTACT: 'DELETE_CONTACT',

  // Interactions
  SET_INTERACTIONS: 'SET_INTERACTIONS',
  ADD_INTERACTION: 'ADD_INTERACTION',
  UPDATE_INTERACTION: 'UPDATE_INTERACTION',

  // Reminder Instances
  SET_REMINDER_INSTANCES: 'SET_REMINDER_INSTANCES',
  ADD_REMINDER_INSTANCE: 'ADD_REMINDER_INSTANCE',
  UPDATE_REMINDER_INSTANCE: 'UPDATE_REMINDER_INSTANCE',
  DELETE_REMINDER_INSTANCE: 'DELETE_REMINDER_INSTANCE',

  // Snooze Data
  SET_SNOOZE_DATA: 'SET_SNOOZE_DATA',
  UPDATE_SNOOZE_DATA: 'UPDATE_SNOOZE_DATA',

  // User preferences
  SET_USER_PREFERENCES: 'SET_USER_PREFERENCES',
  UPDATE_USER_PREFERENCES: 'UPDATE_USER_PREFERENCES'
};

// Initial state
const initialState = {
  loading: false,
  error: null,
  contacts: [],
  interactions: [],
  reminderInstances: [],
  snoozeData: {},
  userPreferences: {
    hasCompletedOnboarding: false
  }
};

// Reducer function
function appReducer(state, action) {
  switch (action.type) {
    case ACTION_TYPES.SET_LOADING:
      return { ...state, loading: action.payload };

    case ACTION_TYPES.SET_ERROR:
      return { ...state, error: action.payload, loading: false };

    case ACTION_TYPES.SET_CONTACTS:
      return { ...state, contacts: action.payload };

    case ACTION_TYPES.ADD_CONTACT:
      return { ...state, contacts: [...state.contacts, action.payload] };

    case ACTION_TYPES.UPDATE_CONTACT:
      return {
        ...state,
        contacts: state.contacts.map(contact =>
          contact.id === action.payload.id ? action.payload : contact
        )
      };

    case ACTION_TYPES.DELETE_CONTACT:
      return {
        ...state,
        contacts: state.contacts.filter(contact => contact.id !== action.payload),
        interactions: state.interactions.filter(interaction => interaction.contactId !== action.payload),
        snoozeData: Object.fromEntries(
          Object.entries(state.snoozeData).filter(([contactId]) => contactId !== action.payload)
        )
      };

    case ACTION_TYPES.SET_INTERACTIONS:
      return { ...state, interactions: action.payload };

    case ACTION_TYPES.ADD_INTERACTION:
      return { ...state, interactions: [...state.interactions, action.payload] };

    case ACTION_TYPES.UPDATE_INTERACTION:
      return {
        ...state,
        interactions: state.interactions.map(interaction =>
          interaction.id === action.payload.id ? action.payload : interaction
        )
      };

    case ACTION_TYPES.SET_REMINDER_INSTANCES:
      return { ...state, reminderInstances: action.payload };

    case ACTION_TYPES.ADD_REMINDER_INSTANCE:
      return { ...state, reminderInstances: [...state.reminderInstances, action.payload] };

    case ACTION_TYPES.UPDATE_REMINDER_INSTANCE:
      return {
        ...state,
        reminderInstances: state.reminderInstances.map(instance =>
          instance.id === action.payload.id ? action.payload : instance
        )
      };

    case ACTION_TYPES.DELETE_REMINDER_INSTANCE:
      return {
        ...state,
        reminderInstances: state.reminderInstances.filter(instance => instance.id !== action.payload)
      };

    case ACTION_TYPES.SET_SNOOZE_DATA:
      return { ...state, snoozeData: action.payload };

    case ACTION_TYPES.UPDATE_SNOOZE_DATA:
      return { ...state, snoozeData: { ...state.snoozeData, ...action.payload } };

    case ACTION_TYPES.SET_USER_PREFERENCES:
      return { ...state, userPreferences: action.payload };

    case ACTION_TYPES.UPDATE_USER_PREFERENCES:
      return {
        ...state,
        userPreferences: { ...state.userPreferences, ...action.payload }
      };

    default:
      return state;
  }
}

// Create context
const AppContext = createContext();

// Context provider component
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });

      const [contacts, interactions, reminderInstances, snoozeData, userPreferences] = await Promise.all([
        dataService.getContacts(),
        dataService.getInteractions(),
        dataService.getReminderInstances(),
        snoozeDataService.getSnoozeData(),
        dataService.getUserPreferences()
      ]);

      dispatch({ type: ACTION_TYPES.SET_CONTACTS, payload: contacts });
      dispatch({ type: ACTION_TYPES.SET_INTERACTIONS, payload: interactions });
      dispatch({ type: ACTION_TYPES.SET_REMINDER_INSTANCES, payload: reminderInstances });
      dispatch({ type: ACTION_TYPES.SET_SNOOZE_DATA, payload: snoozeData });
      dispatch({ type: ACTION_TYPES.SET_USER_PREFERENCES, payload: userPreferences });

      // Clean up legacy storage on app load
      await dataService.clearLegacyReminders();
      await dataService.clearLegacySnoozeData();

    } catch (error) {
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
    } finally {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    }
  };

  // Action creators
  const actions = {
    // Contact actions
    addContact: async (contactData) => {
      try {
        // Get the circle information for this contact
        const circle = DUNBAR_CIRCLES[contactData.circle];

        // Create a recurring reminder rule based on the Dunbar circle frequency
        const reminderRule = new ReminderRule({
          contactId: contactData.id,
          frequency: circle.frequency
        });

        // Add the reminder rule
        const contactWithReminderData = {
          ...contactData,
          recurringReminder: reminderRule,
          lastInteracted: null // No interactions yet
        };

        // Create a proper Contact instance
        const contact = new Contact(contactWithReminderData);

        await dataService.saveContact(contact);
        dispatch({ type: ACTION_TYPES.ADD_CONTACT, payload: contact });

        // Generate initial reminder instance if enabled
        if (REMINDER_CONFIG.AUTO_GENERATE.ON_CONTACT_ADD) {
          const reminderInstance = generateInitialReminderInstance(contact, state.interactions);
          if (reminderInstance) {
            await dataService.saveReminderInstance(reminderInstance);
            dispatch({ type: ACTION_TYPES.ADD_REMINDER_INSTANCE, payload: reminderInstance });
          }
        }

        return contact;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    updateContact: async (contact) => {
      try {
        // Get the previous contact state for comparison
        const previousContact = state.contacts.find(c => c.id === contact.id);

        const updatedContact = await dataService.saveContact(contact);
        dispatch({ type: ACTION_TYPES.UPDATE_CONTACT, payload: updatedContact });

        // Sync reminder instances if enabled
        if (REMINDER_CONFIG.AUTO_GENERATE.ON_CONTACT_UPDATE) {
          const { instancesToAdd, instancesToUpdate, instancesToDelete } = syncContactReminderInstances(
            updatedContact,
            previousContact,
            state.interactions,
            state.reminderInstances
          );

          // Process deletions
          for (const instance of instancesToDelete) {
            await dataService.deleteReminderInstance(instance.id);
            dispatch({ type: ACTION_TYPES.DELETE_REMINDER_INSTANCE, payload: instance.id });
          }

          // Process updates
          for (const instance of instancesToUpdate) {
            await dataService.saveReminderInstance(instance);
            dispatch({ type: ACTION_TYPES.UPDATE_REMINDER_INSTANCE, payload: instance });
          }

          // Process additions
          for (const instance of instancesToAdd) {
            await dataService.saveReminderInstance(instance);
            dispatch({ type: ACTION_TYPES.ADD_REMINDER_INSTANCE, payload: instance });
          }
        }

        return updatedContact;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    deleteContact: async (contactId) => {
      try {
        // Clean up reminder instances if enabled
        if (REMINDER_CONFIG.AUTO_GENERATE.ON_CONTACT_DELETE) {
          const instancesToDelete = cleanupContactReminderInstances(contactId, state.reminderInstances);

          // Delete reminder instances first
          for (const instance of instancesToDelete) {
            await dataService.deleteReminderInstance(instance.id);
            dispatch({ type: ACTION_TYPES.DELETE_REMINDER_INSTANCE, payload: instance.id });
          }
        }

        await dataService.deleteContact(contactId);
        dispatch({ type: ACTION_TYPES.DELETE_CONTACT, payload: contactId });
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Interaction actions
    addInteraction: async (interactionData) => {
      try {
        const interaction = await dataService.saveInteraction(interactionData);
        dispatch({ type: ACTION_TYPES.ADD_INTERACTION, payload: interaction });

        // Update contact's lastInteracted field
        const contact = state.contacts.find(c => c.id === interaction.contactId);
        if (contact) {
          const updatedContact = new Contact({
            ...contact,
            lastInteracted: interaction.date,
            updatedAt: new Date()
          });

          await dataService.saveContact(updatedContact);
          dispatch({ type: ACTION_TYPES.UPDATE_CONTACT, payload: updatedContact });

          // Handle reminder instance completion and generation if enabled
          if (REMINDER_CONFIG.AUTO_GENERATE.ON_INTERACTION_LOG) {
            const { instancesToUpdate, instancesToAdd } = handleInteractionLogged(
              updatedContact,
              interaction,
              state.reminderInstances
            );

            // Update completed instances
            for (const instance of instancesToUpdate) {
              await dataService.saveReminderInstance(instance);
              dispatch({ type: ACTION_TYPES.UPDATE_REMINDER_INSTANCE, payload: instance });
            }

            // Add new instances
            for (const instance of instancesToAdd) {
              await dataService.saveReminderInstance(instance);
              dispatch({ type: ACTION_TYPES.ADD_REMINDER_INSTANCE, payload: instance });
            }
          }
        }

        return interaction;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    updateInteraction: async (interactionData) => {
      try {
        const updatedInteraction = await dataService.saveInteraction(interactionData);
        dispatch({ type: ACTION_TYPES.UPDATE_INTERACTION, payload: updatedInteraction });

        // Update contact's lastInteracted field if this is the most recent interaction
        const contact = state.contacts.find(c => c.id === updatedInteraction.contactId);
        if (contact) {
          const contactInteractions = state.interactions.filter(i => i.contactId === contact.id);
          const mostRecentInteraction = contactInteractions
            .map(i => i.id === updatedInteraction.id ? updatedInteraction : i)
            .sort((a, b) => new Date(b.date) - new Date(a.date))[0];

          if (mostRecentInteraction && mostRecentInteraction.id === updatedInteraction.id) {
            const updatedContact = new Contact({
              ...contact,
              lastInteracted: updatedInteraction.date,
              updatedAt: new Date()
            });

            await dataService.saveContact(updatedContact);
            dispatch({ type: ACTION_TYPES.UPDATE_CONTACT, payload: updatedContact });
          }
        }

        return updatedInteraction;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Reminder Instance actions
    completeReminderInstance: async (instanceId, completionDate = new Date()) => {
      try {
        const instance = state.reminderInstances.find(i => i.id === instanceId);
        if (!instance) {
          throw new Error('Reminder instance not found');
        }

        const contact = state.contacts.find(c => c.id === instance.contactId);
        if (!contact) {
          throw new Error('Contact not found for reminder instance');
        }

        const { instanceToUpdate, instanceToAdd } = completeReminderInstance(
          instance,
          contact,
          completionDate
        );

        // Update completed instance
        if (instanceToUpdate) {
          await dataService.saveReminderInstance(instanceToUpdate);
          dispatch({ type: ACTION_TYPES.UPDATE_REMINDER_INSTANCE, payload: instanceToUpdate });
        }

        // Add next instance if recurring
        if (instanceToAdd) {
          await dataService.saveReminderInstance(instanceToAdd);
          dispatch({ type: ACTION_TYPES.ADD_REMINDER_INSTANCE, payload: instanceToAdd });
        }

        return { completed: instanceToUpdate, next: instanceToAdd };
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    snoozeReminderInstance: async (instanceId, snoozeDuration) => {
      try {
        const instance = state.reminderInstances.find(i => i.id === instanceId);
        if (!instance) {
          throw new Error('Reminder instance not found');
        }

        const snoozeUntilDate = new Date();
        snoozeUntilDate.setDate(snoozeUntilDate.getDate() + snoozeDuration);

        instance.snoozeUntil(snoozeUntilDate);

        await dataService.saveReminderInstance(instance);
        dispatch({ type: ACTION_TYPES.UPDATE_REMINDER_INSTANCE, payload: instance });

        return instance;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    unsnoozeReminderInstance: async (instanceId) => {
      try {
        const instance = state.reminderInstances.find(i => i.id === instanceId);
        if (!instance) {
          throw new Error('Reminder instance not found');
        }

        instance.unsnooze();

        await dataService.saveReminderInstance(instance);
        dispatch({ type: ACTION_TYPES.UPDATE_REMINDER_INSTANCE, payload: instance });

        return instance;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },




    // User preferences actions
    updateUserPreferences: async (preferences) => {
      try {
        const updatedPreferences = await dataService.saveUserPreferences({
          ...state.userPreferences,
          ...preferences
        });
        dispatch({ type: ACTION_TYPES.UPDATE_USER_PREFERENCES, payload: preferences });
        return updatedPreferences;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Reminder Instance Helper Functions
    getActiveReminderInstances: () => {
      return state.reminderInstances.filter(instance => instance.isActive);
    },

    getHomePageReminderInstances: () => {
      const now = new Date();
      const fiveDaysFromNow = new Date();
      fiveDaysFromNow.setDate(now.getDate() + REMINDER_CONFIG.HOME_PREVIEW_DAYS);

      return state.reminderInstances.filter(instance => {
        if (instance.state !== 'PENDING' || instance.isSnoozed) return false;
        return instance.isOverdue || (instance.isUpcoming && instance.reminderDate <= fiveDaysFromNow);
      });
    },

    getContactReminderInstance: (contactId) => {
      return state.reminderInstances.find(
        instance => instance.contactId === contactId && instance.state === 'PENDING'
      );
    },

    isContactOverdueByInstance: (contactId) => {
      const instance = state.reminderInstances.find(
        instance => instance.contactId === contactId && instance.state === 'PENDING'
      );
      return instance ? instance.isOverdue && !instance.isSnoozed : false;
    },

    // CALCULATED REMINDER ACTIONS - In-Memory Reminder Management (Legacy)

    // Snooze a reminder for a contact
    snoozeContactReminder: async (contactId, duration) => {
      try {
        const snoozedUntilDate = await snoozeDataService.snoozeContact(contactId, duration);

        // Update local snooze data
        const updatedSnoozeData = {
          [contactId]: {
            snoozedUntilDate: snoozedUntilDate,
            snoozedAt: new Date(),
            duration: duration
          }
        };
        dispatch({ type: ACTION_TYPES.UPDATE_SNOOZE_DATA, payload: updatedSnoozeData });

        return snoozedUntilDate;
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Remove snooze for a contact
    unsnoozeContactReminder: async (contactId) => {
      try {
        await snoozeDataService.unsnoozeContact(contactId);

        // Update local snooze data
        const updatedSnoozeData = { ...state.snoozeData };
        delete updatedSnoozeData[contactId];
        dispatch({ type: ACTION_TYPES.SET_SNOOZE_DATA, payload: updatedSnoozeData });
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    // Get active reminders for display
    getActiveReminders: () => {
      return calculateDisplayReminders(state.contacts, state.interactions, state.snoozeData);
    },

    // Get home page reminders (overdue + upcoming within 5 days)
    getHomePageReminders: () => {
      return calculateHomePageReminders(state.contacts, state.interactions, state.snoozeData);
    },

    // Get reminder for a specific contact
    getContactReminder: (contactId) => {
      return getContactReminder(contactId, state.contacts, state.interactions, state.snoozeData);
    },

    // Check if a contact is overdue
    isContactOverdue: (contactId) => {
      return isContactOverdue(contactId, state.contacts, state.interactions, state.snoozeData);
    },

    // Calculate next reminder date for a contact after an interaction
    calculateNextReminderDate: (contactId, interactionDate) => {
      const contact = state.contacts.find(c => c.id === contactId);
      if (!contact) return null;
      return calculateNextReminderDate(contact, interactionDate);
    },

    // DEVELOPMENT ONLY - Load test data
    loadTestData: async () => {
      try {
        if (!testDataService.isDevelopmentMode()) {
          throw new Error('Test data loading is only available in development mode');
        }

        dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });

        // Clear existing data
        await dataService.clearAllData();
        await snoozeDataService.clearAllSnoozeData();

        // Generate test data
        const { contacts, interactions } = testDataService.generateTestData();

        // Save test data
        for (const contact of contacts) {
          await dataService.saveContact(contact);
        }

        for (const interaction of interactions) {
          await dataService.saveInteraction(interaction);
        }

        // Update state
        dispatch({ type: ACTION_TYPES.SET_CONTACTS, payload: contacts });
        dispatch({ type: ACTION_TYPES.SET_INTERACTIONS, payload: interactions });
        dispatch({ type: ACTION_TYPES.SET_SNOOZE_DATA, payload: {} });

        return { contacts, interactions };
      } catch (error) {
        dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
        throw error;
      } finally {
        dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
      }
    },

    // Utility actions
    clearError: () => {
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null });
    },

    refreshData: () => {
      loadInitialData();
    }
  };

  const value = {
    state,
    actions
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
