import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Card, 
  Text, 
  Surface,
  IconButton,
  useTheme 
} from 'react-native-paper';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';
import { useApp } from '../context/AppContext';

export default function ContactReminderStatus({ contact }) {
  const theme = useTheme();
  const { state } = useApp();

  // Calculate reminder status
  const reminderStatus = useMemo(() => {
    const now = new Date();
    const circle = DUNBAR_CIRCLES[contact.circle];



    // Calculate based on last interaction and circle frequency
    const contactInteractions = state.interactions.filter(i => i.contactId === contact.id);
    
    if (contactInteractions.length === 0) {
      // No interactions - calculate from contact creation date
      const createdDate = new Date(contact.createdAt || now);
      const daysSinceCreated = Math.ceil((now - createdDate) / (1000 * 60 * 60 * 24));
      const frequency = circle.frequency;
      
      if (daysSinceCreated > frequency) {
        return {
          type: 'overdue',
          title: 'Contact Overdue',
          message: `No interactions for ${daysSinceCreated} days (recommended: ${frequency} days)`,
          icon: 'account-alert',
          color: '#FF6B6B'
        };
      } else {
        const daysUntilDue = frequency - daysSinceCreated;
        return {
          type: 'upcoming',
          title: 'Contact Due Soon',
          message: `Recommended contact in ${daysUntilDue} day${daysUntilDue === 1 ? '' : 's'}`,
          icon: 'account-clock',
          color: '#4CAF50'
        };
      }
    }

    // Find most recent interaction
    const lastInteraction = contactInteractions.reduce((latest, current) => 
      new Date(current.date) > new Date(latest.date) ? current : latest
    );

    const lastInteractionDate = new Date(lastInteraction.date);
    const daysSinceLastContact = Math.ceil((now - lastInteractionDate) / (1000 * 60 * 60 * 24));
    const frequency = circle.frequency;

    if (daysSinceLastContact > frequency) {
      const overdueDays = daysSinceLastContact - frequency;
      return {
        type: 'overdue',
        title: 'Contact Overdue',
        message: `${overdueDays} day${overdueDays === 1 ? '' : 's'} overdue (last contact: ${daysSinceLastContact} days ago)`,
        lastInteractionDate,
        icon: 'account-alert',
        color: '#FF6B6B'
      };
    } else {
      const daysUntilDue = frequency - daysSinceLastContact;
      if (daysUntilDue <= 3) {
        return {
          type: 'upcoming',
          title: 'Contact Due Soon',
          message: `Due in ${daysUntilDue} day${daysUntilDue === 1 ? '' : 's'} (last contact: ${daysSinceLastContact} days ago)`,
          lastInteractionDate,
          icon: 'account-clock',
          color: '#FF9800'
        };
      } else {
        return {
          type: 'good',
          title: 'Contact Up to Date',
          message: `Next contact due in ${daysUntilDue} days (last contact: ${daysSinceLastContact} days ago)`,
          lastInteractionDate,
          icon: 'account-check',
          color: '#4CAF50'
        };
      }
    }
  }, [contact, state.interactions]);

  // Get frequency display text
  const getFrequencyText = () => {
    const circle = DUNBAR_CIRCLES[contact.circle];
    return `Every ${circle.frequency} days (${circle.name} default)`;
  };

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Next Reminder
          </Text>
        </View>

        <View style={styles.statusContainer}>
          <Surface 
            style={[styles.iconContainer, { backgroundColor: reminderStatus.color + '20' }]} 
            elevation={1}
          >
            <IconButton 
              icon={reminderStatus.icon} 
              size={24}
              iconColor={reminderStatus.color}
              style={styles.statusIcon}
            />
          </Surface>
          
          <View style={styles.statusInfo}>
            <Text 
              variant="titleMedium" 
              style={[styles.statusTitle, { color: reminderStatus.color }]}
            >
              {reminderStatus.title}
            </Text>
            <Text variant="bodyMedium" style={styles.statusMessage}>
              {reminderStatus.message}
            </Text>
          </View>
        </View>

        <View style={styles.frequencyContainer}>
          <Text variant="bodySmall" style={styles.frequencyLabel}>
            Contact Frequency:
          </Text>
          <Text variant="bodySmall" style={styles.frequencyText}>
            {getFrequencyText()}
          </Text>
        </View>

        {reminderStatus.date && (
          <View style={styles.dateContainer}>
            <Text variant="bodySmall" style={styles.dateLabel}>
              Reminder Date:
            </Text>
            <Text variant="bodySmall" style={styles.dateText}>
              {reminderStatus.date.toLocaleDateString()}
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    borderRadius: 24,
    marginRight: 12,
  },
  statusIcon: {
    margin: 0,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontWeight: '600',
    marginBottom: 4,
  },
  statusMessage: {
    lineHeight: 18,
    opacity: 0.8,
  },
  frequencyContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  frequencyLabel: {
    fontWeight: '600',
    marginRight: 8,
    opacity: 0.7,
  },
  frequencyText: {
    opacity: 0.8,
  },
  dateContainer: {
    flexDirection: 'row',
  },
  dateLabel: {
    fontWeight: '600',
    marginRight: 8,
    opacity: 0.7,
  },
  dateText: {
    opacity: 0.8,
  },
});
