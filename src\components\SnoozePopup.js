import React from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Dialog, 
  Portal, 
  Text, 
  Button,
  List,
  Divider 
} from 'react-native-paper';

const SNOOZE_OPTIONS = [
  {
    id: '1day',
    title: 'Snooze for 1 day',
    subtitle: 'Remind me tomorrow',
    icon: 'clock-time-one'
  },
  {
    id: '1week',
    title: 'Snooze for 1 week',
    subtitle: 'Remind me next week',
    icon: 'clock-time-seven'
  },
  {
    id: '1month',
    title: 'Snooze for 1 month',
    subtitle: 'Remind me next month',
    icon: 'calendar-month'
  },
  {
    id: '1year',
    title: 'Snooze for 1 year',
    subtitle: 'Remind me next year',
    icon: 'calendar'
  },
  {
    id: 'indefinite',
    title: 'Snooze indefinitely',
    subtitle: 'Don\'t remind me again',
    icon: 'clock-remove'
  }
];

export default function SnoozePopup({ 
  visible, 
  onDismiss, 
  onSnooze, 
  contactName 
}) {

  const handleSnoozeOption = (duration) => {
    if (onSnooze) {
      onSnooze(duration);
    }
    onDismiss();
  };

  return (
    <Portal>
      <Dialog 
        visible={visible} 
        onDismiss={onDismiss}
        style={styles.dialog}
      >
        <Dialog.Title style={styles.title}>
          Snooze Reminder
        </Dialog.Title>
        
        <Dialog.Content>
          {contactName && (
            <Text variant="bodyMedium" style={styles.subtitle}>
              How long would you like to snooze the reminder for {contactName}?
            </Text>
          )}
          
          <View style={styles.optionsContainer}>
            {SNOOZE_OPTIONS.map((option, index) => (
              <View key={option.id}>
                <List.Item
                  title={option.title}
                  description={option.subtitle}
                  left={props => (
                    <List.Icon 
                      {...props} 
                      icon={option.icon}
                      color="#666"
                    />
                  )}
                  onPress={() => handleSnoozeOption(option.id)}
                  style={styles.optionItem}
                  titleStyle={styles.optionTitle}
                  descriptionStyle={styles.optionDescription}
                />
                {index < SNOOZE_OPTIONS.length - 1 && (
                  <Divider style={styles.divider} />
                )}
              </View>
            ))}
          </View>
        </Dialog.Content>
        
        <Dialog.Actions>
          <Button onPress={onDismiss}>
            Cancel
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}

const styles = StyleSheet.create({
  dialog: {
    maxWidth: 400,
    alignSelf: 'center',
  },
  title: {
    textAlign: 'center',
    fontWeight: 'bold',
  },
  subtitle: {
    marginBottom: 16,
    opacity: 0.8,
    lineHeight: 20,
  },
  optionsContainer: {
    marginTop: 8,
  },
  optionItem: {
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
  divider: {
    marginVertical: 4,
  },
});
