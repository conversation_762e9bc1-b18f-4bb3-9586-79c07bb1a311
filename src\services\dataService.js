import AsyncStorage from '@react-native-async-storage/async-storage';
import { Contact, Interaction, ReminderInstance } from '../utils/dataModels';

const STORAGE_KEYS = {
  CONTACTS: '@relateful_contacts',
  INTERACTIONS: '@relateful_interactions',
  USER_PREFERENCES: '@relateful_preferences',
  REMINDER_INSTANCES: '@relateful_reminder_instances'
};

class DataService {
  // Contact operations
  async getContacts() {
    try {
      const contactsJson = await AsyncStorage.getItem(STORAGE_KEYS.CONTACTS);
      if (!contactsJson) return [];
      
      const contactsData = JSON.parse(contactsJson);
      return contactsData.map(data => Contact.fromJSON(data));
    } catch (error) {
      console.error('Error getting contacts:', error);
      return [];
    }
  }

  async saveContact(contact) {
    try {
      const contacts = await this.getContacts();
      const existingIndex = contacts.findIndex(c => c.id === contact.id);
      
      contact.updatedAt = new Date();
      
      if (existingIndex >= 0) {
        contacts[existingIndex] = contact;
      } else {
        contacts.push(contact);
      }
      
      await AsyncStorage.setItem(STORAGE_KEYS.CONTACTS, JSON.stringify(contacts.map(c => c.toJSON())));
      return contact;
    } catch (error) {
      console.error('Error saving contact:', error);
      throw error;
    }
  }

  async deleteContact(contactId) {
    try {
      const contacts = await this.getContacts();
      const filteredContacts = contacts.filter(c => c.id !== contactId);

      await AsyncStorage.setItem(STORAGE_KEYS.CONTACTS, JSON.stringify(filteredContacts.map(c => c.toJSON())));

      // Also delete related interactions and reminder instances
      await this.deleteInteractionsByContactId(contactId);
      await this.deleteReminderInstancesByContactId(contactId);

      return true;
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  }

  // Interaction operations
  async getInteractions() {
    try {
      const interactionsJson = await AsyncStorage.getItem(STORAGE_KEYS.INTERACTIONS);
      if (!interactionsJson) return [];
      
      const interactionsData = JSON.parse(interactionsJson);
      return interactionsData.map(data => Interaction.fromJSON(data));
    } catch (error) {
      console.error('Error getting interactions:', error);
      return [];
    }
  }

  async getInteractionsByContactId(contactId) {
    try {
      const interactions = await this.getInteractions();
      return interactions.filter(i => i.contactId === contactId);
    } catch (error) {
      console.error('Error getting interactions by contact:', error);
      return [];
    }
  }

  async saveInteraction(interaction) {
    try {
      const interactions = await this.getInteractions();
      const existingIndex = interactions.findIndex(i => i.id === interaction.id);
      
      if (existingIndex >= 0) {
        interactions[existingIndex] = interaction;
      } else {
        interactions.push(interaction);
      }
      
      await AsyncStorage.setItem(STORAGE_KEYS.INTERACTIONS, JSON.stringify(interactions.map(i => i.toJSON())));
      return interaction;
    } catch (error) {
      console.error('Error saving interaction:', error);
      throw error;
    }
  }

  async deleteInteractionsByContactId(contactId) {
    try {
      const interactions = await this.getInteractions();
      const filteredInteractions = interactions.filter(i => i.contactId !== contactId);
      
      await AsyncStorage.setItem(STORAGE_KEYS.INTERACTIONS, JSON.stringify(filteredInteractions.map(i => i.toJSON())));
      return true;
    } catch (error) {
      console.error('Error deleting interactions:', error);
      throw error;
    }
  }

  // Reminder Instance operations
  async getReminderInstances() {
    try {
      const instancesJson = await AsyncStorage.getItem(STORAGE_KEYS.REMINDER_INSTANCES);
      if (!instancesJson) return [];

      const instancesData = JSON.parse(instancesJson);
      return instancesData.map(data => ReminderInstance.fromJSON(data));
    } catch (error) {
      console.error('Error getting reminder instances:', error);
      return [];
    }
  }

  async saveReminderInstance(reminderInstance) {
    try {
      const instances = await this.getReminderInstances();
      const existingIndex = instances.findIndex(i => i.id === reminderInstance.id);

      if (existingIndex >= 0) {
        instances[existingIndex] = reminderInstance;
      } else {
        instances.push(reminderInstance);
      }

      await AsyncStorage.setItem(STORAGE_KEYS.REMINDER_INSTANCES, JSON.stringify(instances.map(i => i.toJSON())));
      return reminderInstance;
    } catch (error) {
      console.error('Error saving reminder instance:', error);
      throw error;
    }
  }

  async deleteReminderInstance(instanceId) {
    try {
      const instances = await this.getReminderInstances();
      const filteredInstances = instances.filter(i => i.id !== instanceId);

      await AsyncStorage.setItem(STORAGE_KEYS.REMINDER_INSTANCES, JSON.stringify(filteredInstances.map(i => i.toJSON())));
      return true;
    } catch (error) {
      console.error('Error deleting reminder instance:', error);
      throw error;
    }
  }

  async getReminderInstancesByContactId(contactId) {
    try {
      const instances = await this.getReminderInstances();
      return instances.filter(i => i.contactId === contactId);
    } catch (error) {
      console.error('Error getting reminder instances by contact ID:', error);
      return [];
    }
  }

  async deleteReminderInstancesByContactId(contactId) {
    try {
      const instances = await this.getReminderInstances();
      const filteredInstances = instances.filter(i => i.contactId !== contactId);

      await AsyncStorage.setItem(STORAGE_KEYS.REMINDER_INSTANCES, JSON.stringify(filteredInstances.map(i => i.toJSON())));
      return true;
    } catch (error) {
      console.error('Error deleting reminder instances by contact ID:', error);
      throw error;
    }
  }

  async getActiveReminderInstances() {
    try {
      const instances = await this.getReminderInstances();
      return instances.filter(i => i.state === 'PENDING');
    } catch (error) {
      console.error('Error getting active reminder instances:', error);
      return [];
    }
  }

  async getCompletedReminderInstances() {
    try {
      const instances = await this.getReminderInstances();
      return instances.filter(i => i.state === 'COMPLETED');
    } catch (error) {
      console.error('Error getting completed reminder instances:', error);
      return [];
    }
  }

  // Legacy cleanup utilities
  async clearLegacyReminders() {
    try {
      await AsyncStorage.removeItem('@relateful_reminders');
      console.log('Legacy reminders storage cleared');
      return true;
    } catch (error) {
      console.error('Error clearing legacy reminders:', error);
      return false;
    }
  }

  async clearLegacySnoozeData() {
    try {
      await AsyncStorage.removeItem('@relateful_snooze_data');
      console.log('Legacy snooze data storage cleared');
      return true;
    } catch (error) {
      console.error('Error clearing legacy snooze data:', error);
      return false;
    }
  }

  // User preferences
  async getUserPreferences() {
    try {
      const preferencesJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      if (!preferencesJson) return { hasCompletedOnboarding: false };
      
      return JSON.parse(preferencesJson);
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return { hasCompletedOnboarding: false };
    }
  }

  async saveUserPreferences(preferences) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
      return preferences;
    } catch (error) {
      console.error('Error saving user preferences:', error);
      throw error;
    }
  }

  // Utility methods
  async clearAllData() {
    try {
      await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
      // Also clear legacy data
      await this.clearLegacyReminders();
      await this.clearLegacySnoozeData();
      return true;
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }
}

export default new DataService();
