import React, { useState, useMemo } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import {
  Appbar,
  Searchbar,
  Chip,
  Text,
  List,
  Avatar,
  Surface,
  IconButton,
  useTheme,
  FAB
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { DUNBAR_CIRCLES, INTERACTION_TYPES } from '../constants/dunbarCircles';

export default function LogScreen({ navigation }) {
  const { state } = useApp();
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Get all interactions with contact information
  const enrichedInteractions = useMemo(() => {
    return state.interactions.map(interaction => {
      const contact = state.contacts.find(c => c.id === interaction.contactId);
      return {
        ...interaction,
        contactName: contact?.name || 'Unknown Contact',
        contactCircle: contact?.circle || 'full'
      };
    }).sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, most recent first
  }, [state.interactions, state.contacts]);

  // Filter interactions based on search and filter
  const filteredInteractions = useMemo(() => {
    let filtered = enrichedInteractions;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(interaction => 
        interaction.contactName.toLowerCase().includes(query) ||
        interaction.notes.toLowerCase().includes(query) ||
        new Date(interaction.date).toLocaleDateString().includes(query)
      );
    }

    // Apply circle filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(interaction => interaction.contactCircle === selectedFilter);
    }

    return filtered;
  }, [enrichedInteractions, searchQuery, selectedFilter]);

  // Filter options
  const filterOptions = [
    { key: 'all', label: 'All', count: enrichedInteractions.length },
    ...Object.entries(DUNBAR_CIRCLES).map(([key, circle]) => ({
      key,
      label: circle.name,
      count: enrichedInteractions.filter(i => i.contactCircle === key).length
    }))
  ];

  // Render individual interaction item
  const renderInteractionItem = ({ item }) => {
    const interactionType = INTERACTION_TYPES[item.type];
    const interactionDate = new Date(item.date);
    const circle = DUNBAR_CIRCLES[item.contactCircle];

    const getRelativeDate = () => {
      const now = new Date();
      const daysDiff = Math.ceil((now - interactionDate) / (1000 * 60 * 60 * 24));

      if (daysDiff === 0) return 'Today';
      if (daysDiff === 1) return 'Yesterday';
      if (daysDiff < 7) return `${daysDiff} days ago`;
      if (daysDiff < 30) return `${Math.ceil(daysDiff / 7)} weeks ago`;
      return `${Math.ceil(daysDiff / 30)} months ago`;
    };

    const getQualityText = () => {
      switch (item.quality) {
        case 'brief': return 'Brief';
        case 'good': return 'Good';
        case 'deep': return 'Deep';
        default: return 'Good';
      }
    };

    // Build description components
    const descriptionComponents = [];

    // Add circle and interaction type info
    descriptionComponents.push(
      <View key="meta" style={styles.interactionMeta}>
        <Text variant="bodySmall" style={[styles.circleText, { color: circle.color }]}>
          {circle.name}
        </Text>
        <Text variant="bodySmall" style={styles.typeText}>
          📞 {interactionType.name}
        </Text>
        {item.duration && (
          <Text variant="bodySmall" style={styles.durationText}>
            ⏱️ {item.duration} min
          </Text>
        )}
      </View>
    );

    // Add date info
    descriptionComponents.push(
      <Text key="date" variant="bodySmall" style={styles.dateText}>
        {getRelativeDate()} • {interactionDate.toLocaleDateString()}
      </Text>
    );

    // Add quality and notes if present
    if (item.quality !== 'good' || item.notes) {
      const additionalInfo = [];
      if (item.quality !== 'good') {
        additionalInfo.push(`Quality: ${getQualityText()}`);
      }
      if (item.notes) {
        additionalInfo.push(item.notes);
      }

      descriptionComponents.push(
        <Text key="additional" variant="bodySmall" style={styles.notesText} numberOfLines={2}>
          {additionalInfo.join(' • ')}
        </Text>
      );
    }

    return (
      <List.Item
        title={item.contactName}
        description={() => (
          <View style={styles.interactionInfo}>
            {descriptionComponents}
          </View>
        )}
        left={() => (
          <Avatar.Text
            size={48}
            label={item.contactName.charAt(0).toUpperCase()}
            style={{ backgroundColor: circle.color }}
          />
        )}
        right={() => <List.Icon icon="chevron-right" />}
        onPress={() => handleInteractionPress(item)}
        titleStyle={styles.contactName}
        style={styles.interactionListItem}
      />
    );
  };

  const handleInteractionPress = (interaction) => {
    // Navigate to interaction detail screen
    const contact = state.contacts.find(c => c.id === interaction.contactId);
    navigation.navigate('LogDetail', {
      interaction,
      contact: contact ? (contact.toJSON ? contact.toJSON() : contact) : { id: interaction.contactId, name: 'Unknown Contact' }
    });
  };

  const handleAddInteraction = () => {
    // Navigate to contacts screen to select a contact first
    navigation.navigate('Contacts');
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content 
          title={`Interactions (${filteredInteractions.length})`}
        />
      </Appbar.Header>
      
      {/* Search Bar */}
      <Searchbar
        placeholder="Search interactions, contacts, or dates..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      {/* Filter Chips */}
      <Surface style={styles.filtersContainer} elevation={1}>
        <FlatList
          data={filterOptions}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item.key}
          renderItem={({ item }) => {
            const isSelected = selectedFilter === item.key;
            const circleColor = item.key !== 'all' ? DUNBAR_CIRCLES[item.key].color : '#45B7D1';

            return (
              <Chip
                mode={isSelected ? 'flat' : 'outlined'}
                selected={isSelected}
                onPress={() => setSelectedFilter(item.key)}
                style={[
                  styles.filterChip,
                  isSelected && { backgroundColor: circleColor + '20' }
                ]}
                textStyle={[
                  styles.filterChipText,
                  isSelected && { color: circleColor, fontWeight: 'bold' }
                ]}
              >
                {item.label} ({item.count})
              </Chip>
            );
          }}
        />
      </Surface>

      {/* Interactions List */}
      {filteredInteractions.length === 0 ? (
        <View style={styles.emptyState}>
          <IconButton
            icon="message-outline"
            size={64}
            iconColor="#ccc"
          />
          <Text variant="headlineSmall" style={styles.emptyTitle}>
            {searchQuery || selectedFilter !== 'all' ? 'No Matching Interactions' : 'No Interactions Yet'}
          </Text>
          <Text variant="bodyLarge" style={styles.emptySubtitle}>
            {searchQuery || selectedFilter !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'Start logging interactions with your contacts to see them here.'
            }
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredInteractions}
          renderItem={renderInteractionItem}
          keyExtractor={(item) => item.id}
          style={styles.list}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      )}

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={handleAddInteraction}
        label="Log Interaction"
      />
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  searchBar: {
    margin: 16,
    elevation: 2,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  filterChipText: {
    fontSize: 12,
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 80, // Space for FAB
  },
  interactionListItem: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outline,
  },
  contactName: {
    fontWeight: 'bold',
  },
  interactionInfo: {
    marginTop: 4,
    gap: 2,
  },
  interactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
  },
  circleText: {
    fontWeight: '600',
    marginBottom: 2,
  },
  typeText: {
    opacity: 0.8,
    marginBottom: 1,
  },
  durationText: {
    opacity: 0.7,
    marginBottom: 1,
  },
  dateText: {
    opacity: 0.6,
    fontStyle: 'italic',
  },
  notesText: {
    opacity: 0.7,
    lineHeight: 18,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  emptySubtitle: {
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
    lineHeight: 24,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
