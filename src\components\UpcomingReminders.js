import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { 
  Card, 
  Text, 
  Button,
  Surface,
  IconButton 
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { Contact } from '../utils/dataModels';
import ReminderCard from './ReminderCard';
import SnoozePopup from './SnoozePopup';

export default function UpcomingReminders({ navigation }) {
  const { state, actions } = useApp();
  const [reminders, setReminders] = useState([]);
  const [snoozePopupVisible, setSnoozePopupVisible] = useState(false);
  const [selectedReminder, setSelectedReminder] = useState(null);

  // Calculate and update reminders when contacts, interactions, or snooze data change
  useEffect(() => {
    // Get home page reminders (overdue + upcoming within 5 days)
    const homeReminders = actions.getHomePageReminders();
    setReminders(homeReminders);
  }, [state.contacts, state.interactions, state.snoozeData]);

  // Handle snooze button press
  const handleSnoozePress = (reminderId) => {
    const reminder = reminders.find(r => r.id === reminderId);
    if (reminder) {
      setSelectedReminder(reminder);
      setSnoozePopupVisible(true);
    }
  };

  // Handle snooze duration selection
  const handleSnooze = async (duration) => {
    if (selectedReminder) {
      try {
        await actions.snoozeContactReminder(selectedReminder.contactId, duration);
        // Remove the reminder from local state for smooth animation
        setReminders(prev => prev.filter(r => r.id !== selectedReminder.id));
      } catch (error) {
        console.error('Error snoozing reminder:', error);
      }
    }
    setSelectedReminder(null);
  };

  // Handle log interaction button press
  const handleLogPress = async (contact) => {
    try {
      // Find the reminder instance for this contact
      const reminderInstance = reminders.find(r => r.contact.id === contact.id);

      if (reminderInstance) {
        // Mark the reminder as completed
        await actions.completeReminderInstance(reminderInstance.id);

        // Update contact's lastReminderCompleted field
        const updatedContact = new Contact({
          ...contact,
          lastReminderCompleted: new Date(),
          updatedAt: new Date()
        });

        await actions.updateContact(updatedContact);

        // Remove the reminder from the local state
        handleReminderRemove(reminderInstance.id);
      }

      // Navigate to Log Interaction screen
      navigation.navigate('LogInteraction', { contact });
    } catch (error) {
      console.error('Error completing reminder:', error);
      // Still navigate to Log Interaction even if reminder completion fails
      navigation.navigate('LogInteraction', { contact });
    }
  };

  // Handle reminder removal (after animation)
  const handleReminderRemove = (reminderId) => {
    setReminders(prev => prev.filter(r => r.id !== reminderId));
  };

  // Render empty state
  const renderEmptyState = () => (
    <Card style={styles.emptyCard}>
      <Card.Content style={styles.emptyContent}>
        <Surface style={styles.emptyIconContainer} elevation={1}>
          <IconButton 
            icon="check-circle" 
            size={48}
            iconColor="#4CAF50"
          />
        </Surface>
        <Text variant="titleLarge" style={styles.emptyTitle}>
          All caught up!
        </Text>
        <Text variant="bodyMedium" style={styles.emptySubtitle}>
          You have no upcoming reminders. Great job staying connected with your network!
        </Text>
        <Button
          mode="outlined"
          onPress={() => navigation.navigate('Contacts')}
          style={styles.emptyButton}
          icon="account-plus"
        >
          Add More Contacts
        </Button>
      </Card.Content>
    </Card>
  );

  // Render reminder item
  const renderReminderItem = ({ item }) => (
    <ReminderCard
      reminder={item}
      onSnooze={handleSnoozePress}
      onLog={handleLogPress}
      onRemove={() => handleReminderRemove(item.id)}
    />
  );

  // Separate overdue and upcoming reminders
  const overdueReminders = reminders.filter(r => r.isOverdue);
  const upcomingReminders = reminders.filter(r => r.isUpcoming);

  return (
    <View style={styles.container}>
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.header}>
            <View style={styles.headerText}>
              <Text variant="titleLarge" style={styles.title}>
                Upcoming Reminders
              </Text>
              <Text variant="bodyMedium" style={styles.subtitle}>
                {reminders.length === 0 
                  ? 'No reminders right now'
                  : `${reminders.length} reminder${reminders.length === 1 ? '' : 's'}`
                }
              </Text>
            </View>
            <IconButton
              icon="refresh"
              size={24}
              onPress={() => {
                const homeReminders = actions.getHomePageReminders();
                setReminders(homeReminders);
              }}
            />
          </View>
        </Card.Content>
      </Card>

      {reminders.length === 0 ? (
        renderEmptyState()
      ) : (
        <View style={styles.remindersContainer}>
          {/* Overdue reminders section */}
          {overdueReminders.length > 0 && (
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Overdue ({overdueReminders.length})
              </Text>
              <FlatList
                data={overdueReminders}
                renderItem={renderReminderItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            </View>
          )}

          {/* Upcoming reminders section */}
          {upcomingReminders.length > 0 && (
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Coming Up ({upcomingReminders.length})
              </Text>
              <FlatList
                data={upcomingReminders}
                renderItem={renderReminderItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            </View>
          )}
        </View>
      )}

      {/* Snooze popup */}
      <SnoozePopup
        visible={snoozePopupVisible}
        onDismiss={() => {
          setSnoozePopupVisible(false);
          setSelectedReminder(null);
        }}
        onSnooze={handleSnooze}
        contactName={selectedReminder?.contact?.name}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  headerCard: {
    marginBottom: 12,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  remindersContainer: {
    gap: 16,
  },
  section: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 8,
    marginLeft: 4,
    opacity: 0.8,
  },
  emptyCard: {
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyIconContainer: {
    borderRadius: 50,
    marginBottom: 16,
  },
  emptyTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
});
