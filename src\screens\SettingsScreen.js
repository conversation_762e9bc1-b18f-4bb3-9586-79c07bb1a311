import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Appbar,
  List,
  Card,
  Button,
  Text,
  Divider,
  useTheme
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import testDataService from '../services/testDataService';

export default function SettingsScreen() {
  const { state, actions } = useApp();
  const theme = useTheme();

  // Create theme-aware styles
  const styles = createStyles(theme);

  const handleClearData = async () => {
    try {
      // TODO: Add confirmation dialog
      await actions.refreshData();
      console.log('Data cleared');
    } catch (error) {
      console.error('Error clearing data:', error);
    }
  };

  const handleLoadTestData = async () => {
    // For web testing, skip the Alert and directly load test data
    if (typeof window !== 'undefined') {
      // Web environment - use window.confirm
      const confirmed = window.confirm(
        'This will clear all existing contacts and interactions and replace them with sample test data. This action cannot be undone. Continue?'
      );

      if (!confirmed) return;

      try {
        await actions.loadTestData();
        window.alert('Test data loaded successfully!');
      } catch (error) {
        console.error('Error loading test data:', error);
        window.alert('Failed to load test data: ' + error.message);
      }
    } else {
      // Native environment - use React Native Alert
      Alert.alert(
        'Load Test Data',
        'This will clear all existing contacts and interactions and replace them with sample test data. This action cannot be undone.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Load Test Data',
            style: 'destructive',
            onPress: async () => {
              try {
                await actions.loadTestData();
                Alert.alert('Success', 'Test data loaded successfully!');
              } catch (error) {
                console.error('Error loading test data:', error);
                Alert.alert('Error', 'Failed to load test data: ' + error.message);
              }
            },
          },
        ]
      );
    }
  };

  const settingsItems = [
    {
      title: 'Notifications',
      subtitle: 'Manage reminder notifications',
      icon: 'bell',
      onPress: () => console.log('Notifications pressed'),
    },
    {
      title: 'Privacy',
      subtitle: 'Data and privacy settings',
      icon: 'shield',
      onPress: () => console.log('Privacy pressed'),
    },
    {
      title: 'About',
      subtitle: 'App version and information',
      icon: 'information',
      onPress: () => console.log('About pressed'),
    },
  ];

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Settings" />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        <Card style={styles.section}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>App Settings</Text>
          </Card.Content>
          {settingsItems.map((item, index) => (
            <List.Item
              key={index}
              title={item.title}
              description={item.subtitle}
              left={props => <List.Icon {...props} icon={item.icon} />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={item.onPress}
            />
          ))}
        </Card>

        <Card style={styles.section}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>Account</Text>
          </Card.Content>
          <List.Item
            title="Onboarding Status"
            description={state.userPreferences.hasCompletedOnboarding ? 'Completed' : 'Not completed'}
            left={props => <List.Icon {...props} icon="account-check" />}
          />
        </Card>

        <Card style={styles.section}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>Data</Text>
            <View style={styles.dataStats}>
              <Text variant="bodyMedium" style={styles.dataText}>Contacts: {state.contacts.length}</Text>
              <Text variant="bodyMedium" style={styles.dataText}>Interactions: {state.interactions.length}</Text>
              <Text variant="bodyMedium" style={styles.dataText}>Snoozed Contacts: {Object.keys(state.snoozeData || {}).length}</Text>
            </View>

            <Button
              mode="outlined"
              onPress={actions.refreshData}
              style={styles.button}
            >
              Refresh Data
            </Button>

            {testDataService.isDevelopmentMode() && (
              <Button
                mode="contained"
                buttonColor="#4ECDC4"
                onPress={handleLoadTestData}
                style={styles.button}
                icon="database"
              >
                Load Test Data
              </Button>
            )}

            <Button
              mode="contained"
              buttonColor="#FF6B6B"
              onPress={handleClearData}
              style={styles.button}
            >
              Clear All Data
            </Button>
          </Card.Content>
        </Card>

        <View style={styles.footer}>
          <Text variant="bodySmall" style={styles.footerText}>Relateful v1.0.0</Text>
          <Text variant="bodySmall" style={styles.footerText}>Made with ❤️ for better relationships</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  dataStats: {
    marginVertical: 16,
  },
  dataText: {
    marginBottom: 8,
  },
  button: {
    marginVertical: 8,
  },
  footer: {
    padding: 32,
    alignItems: 'center',
  },
  footerText: {
    opacity: 0.6,
    marginBottom: 4,
  },
});
