/**
 * Snooze Data Service
 * Manages snooze information for contacts without storing full reminder instances
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = '@relateful_snooze_data';

/**
 * Snooze duration mappings
 */
const SNOOZE_DURATIONS = {
  '1day': 1,
  '1week': 7,
  '1month': 30,
  '1year': 365,
  'indefinite': 36500 // 100 years, effectively indefinite
};

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date with days added
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

class SnoozeDataService {
  /**
   * Get all snooze data
   * @returns {Object} Object with contactId as key and snooze info as value
   */
  async getSnoozeData() {
    try {
      const snoozeJson = await AsyncStorage.getItem(STORAGE_KEY);
      if (!snoozeJson) return {};
      
      const snoozeData = JSON.parse(snoozeJson);
      
      // Convert ISO strings back to Date objects and clean up expired snoozes
      const now = new Date();
      const cleanedData = {};
      
      Object.keys(snoozeData).forEach(contactId => {
        const snooze = snoozeData[contactId];
        if (snooze.snoozedUntilDate) {
          const snoozedUntil = new Date(snooze.snoozedUntilDate);
          
          // Only keep snoozes that haven't expired
          if (snoozedUntil > now) {
            cleanedData[contactId] = {
              ...snooze,
              snoozedUntilDate: snoozedUntil
            };
          }
        }
      });
      
      // Save cleaned data back to storage
      if (Object.keys(cleanedData).length !== Object.keys(snoozeData).length) {
        await this.saveSnoozeData(cleanedData);
      }
      
      return cleanedData;
    } catch (error) {
      console.error('Error getting snooze data:', error);
      return {};
    }
  }

  /**
   * Save snooze data
   * @param {Object} snoozeData - Snooze data object
   */
  async saveSnoozeData(snoozeData) {
    try {
      // Convert Date objects to ISO strings for storage
      const dataForStorage = {};
      Object.keys(snoozeData).forEach(contactId => {
        const snooze = snoozeData[contactId];
        dataForStorage[contactId] = {
          ...snooze,
          snoozedUntilDate: snooze.snoozedUntilDate?.toISOString()
        };
      });
      
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(dataForStorage));
    } catch (error) {
      console.error('Error saving snooze data:', error);
      throw error;
    }
  }

  /**
   * Snooze a reminder for a contact
   * @param {string} contactId - Contact ID
   * @param {string} duration - Snooze duration ('1day', '1week', '1month', '1year', 'indefinite')
   * @returns {Date} Date when snooze expires
   */
  async snoozeContact(contactId, duration) {
    try {
      const days = SNOOZE_DURATIONS[duration];
      if (!days) {
        throw new Error(`Invalid snooze duration: ${duration}`);
      }

      const now = new Date();
      const snoozedUntilDate = addDays(now, days);
      
      const snoozeData = await this.getSnoozeData();
      snoozeData[contactId] = {
        snoozedUntilDate: snoozedUntilDate,
        snoozedAt: now,
        duration: duration
      };
      
      await this.saveSnoozeData(snoozeData);
      return snoozedUntilDate;
    } catch (error) {
      console.error('Error snoozing contact:', error);
      throw error;
    }
  }

  /**
   * Remove snooze for a contact
   * @param {string} contactId - Contact ID
   */
  async unsnoozeContact(contactId) {
    try {
      const snoozeData = await this.getSnoozeData();
      delete snoozeData[contactId];
      await this.saveSnoozeData(snoozeData);
    } catch (error) {
      console.error('Error unsnoozing contact:', error);
      throw error;
    }
  }

  /**
   * Get snooze info for a specific contact
   * @param {string} contactId - Contact ID
   * @returns {Object|null} Snooze info or null if not snoozed
   */
  async getContactSnooze(contactId) {
    try {
      const snoozeData = await this.getSnoozeData();
      return snoozeData[contactId] || null;
    } catch (error) {
      console.error('Error getting contact snooze:', error);
      return null;
    }
  }

  /**
   * Check if a contact is currently snoozed
   * @param {string} contactId - Contact ID
   * @returns {boolean} True if contact is snoozed
   */
  async isContactSnoozed(contactId) {
    try {
      const snooze = await this.getContactSnooze(contactId);
      if (!snooze) return false;
      
      const now = new Date();
      return snooze.snoozedUntilDate > now;
    } catch (error) {
      console.error('Error checking if contact is snoozed:', error);
      return false;
    }
  }

  /**
   * Clean up expired snoozes (called periodically)
   */
  async cleanupExpiredSnoozes() {
    try {
      // This is automatically handled in getSnoozeData()
      await this.getSnoozeData();
    } catch (error) {
      console.error('Error cleaning up expired snoozes:', error);
    }
  }

  /**
   * Clear all snooze data (for testing or reset)
   */
  async clearAllSnoozeData() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing snooze data:', error);
      throw error;
    }
  }
}

export default new SnoozeDataService();
