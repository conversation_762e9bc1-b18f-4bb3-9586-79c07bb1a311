/**
 * In-Memory Reminder Calculation Service
 * Calculates reminders dynamically from contacts and interactions without storing instances
 */

import { CalculatedReminder } from '../utils/dataModels';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';
import { REMINDER_CONFIG } from '../constants/reminderConfig';

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date with days added
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Get days between two dates
 * @param {Date} date1 - First date
 * @param {Date} date2 - Second date
 * @returns {number} Number of days between dates
 */
const getDaysBetween = (date1, date2) => {
  const timeDiff = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
};

/**
 * Get the last interaction date for a contact
 * @param {string} contactId - Contact ID
 * @param {Array} interactions - Array of interactions
 * @returns {Date|null} Last interaction date or null if no interactions
 */
const getLastInteractionDate = (contactId, interactions) => {
  const contactInteractions = interactions
    .filter(interaction => interaction.contactId === contactId)
    .sort((a, b) => new Date(b.date) - new Date(a.date));
  
  return contactInteractions.length > 0 ? new Date(contactInteractions[0].date) : null;
};

/**
 * Calculate all active reminders for display
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Object} snoozeData - Object containing snooze information by contact ID
 * @returns {Array} Array of CalculatedReminder objects
 */
export const calculateActiveReminders = (contacts, interactions, snoozeData = {}) => {
  const now = new Date();
  const reminders = [];

  contacts.forEach(contact => {
    // Skip contacts without recurring reminders
    if (!contact.recurringReminder) return;

    const circle = DUNBAR_CIRCLES[contact.circle];
    if (!circle) return;

    const frequency = contact.recurringReminder.frequency;
    const lastInteractionDate = getLastInteractionDate(contact.id, interactions);
    
    // Calculate due date
    let dueDate;
    if (lastInteractionDate) {
      dueDate = addDays(lastInteractionDate, frequency);
    } else if (contact.lastReminderCompleted) {
      dueDate = addDays(new Date(contact.lastReminderCompleted), frequency);
    } else {
      dueDate = addDays(new Date(contact.createdAt || now), frequency);
    }

    // Calculate days since last contact
    const daysSinceLastContact = lastInteractionDate 
      ? getDaysBetween(lastInteractionDate, now)
      : getDaysBetween(new Date(contact.createdAt || now), now);

    // Determine if overdue or upcoming
    const isOverdue = dueDate < now;
    const isUpcoming = dueDate >= now;

    // Check for snooze data
    const contactSnoozeData = snoozeData[contact.id];
    const snoozedUntilDate = contactSnoozeData?.snoozedUntilDate || null;

    // Create reminder object
    const reminder = new CalculatedReminder({
      contactId: contact.id,
      contact: contact,
      type: 'recurring',
      dueDate: dueDate,
      snoozedUntilDate: snoozedUntilDate,
      isOverdue: isOverdue,
      isUpcoming: isUpcoming,
      daysSinceLastContact: daysSinceLastContact,
      frequency: frequency
    });

    reminders.push(reminder);
  });

  return reminders;
};

/**
 * Calculate reminders for home page display (overdue + upcoming within 5 days)
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Object} snoozeData - Object containing snooze information by contact ID
 * @returns {Array} Array of CalculatedReminder objects for home page
 */
export const calculateHomePageReminders = (contacts, interactions, snoozeData = {}) => {
  const now = new Date();
  const fiveDaysFromNow = addDays(now, REMINDER_CONFIG.HOME_PREVIEW_DAYS);
  
  const allReminders = calculateActiveReminders(contacts, interactions, snoozeData);
  
  // Filter for home page display: overdue or due within 5 days, and not snoozed
  return allReminders.filter(reminder => {
    if (reminder.isSnoozed) return false;
    
    return reminder.isOverdue || 
           (reminder.isUpcoming && reminder.dueDate <= fiveDaysFromNow);
  });
};

/**
 * Calculate reminders for display in reminder list (all active, not snoozed)
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Object} snoozeData - Object containing snooze information by contact ID
 * @returns {Array} Array of CalculatedReminder objects for display
 */
export const calculateDisplayReminders = (contacts, interactions, snoozeData = {}) => {
  const allReminders = calculateActiveReminders(contacts, interactions, snoozeData);
  
  // Filter out snoozed reminders
  return allReminders.filter(reminder => !reminder.isSnoozed);
};

/**
 * Get reminder for a specific contact
 * @param {string} contactId - Contact ID
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Object} snoozeData - Object containing snooze information by contact ID
 * @returns {CalculatedReminder|null} Reminder for the contact or null
 */
export const getContactReminder = (contactId, contacts, interactions, snoozeData = {}) => {
  const contact = contacts.find(c => c.id === contactId);
  if (!contact) return null;

  const allReminders = calculateActiveReminders([contact], interactions, snoozeData);
  return allReminders.length > 0 ? allReminders[0] : null;
};

/**
 * Check if a contact has an overdue reminder
 * @param {string} contactId - Contact ID
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Object} snoozeData - Object containing snooze information by contact ID
 * @returns {boolean} True if contact has overdue reminder
 */
export const isContactOverdue = (contactId, contacts, interactions, snoozeData = {}) => {
  const reminder = getContactReminder(contactId, contacts, interactions, snoozeData);
  return reminder ? reminder.isOverdue && !reminder.isSnoozed : false;
};

/**
 * Calculate next reminder date for a contact after an interaction
 * @param {Object} contact - Contact object
 * @param {Date} interactionDate - Date of the interaction
 * @returns {Date|null} Next reminder date
 */
export const calculateNextReminderDate = (contact, interactionDate) => {
  if (!contact.recurringReminder) return null;
  
  const frequency = contact.recurringReminder.frequency;
  return addDays(new Date(interactionDate), frequency);
};
