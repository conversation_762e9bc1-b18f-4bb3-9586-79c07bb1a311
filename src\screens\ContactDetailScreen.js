import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Appbar,
  Card,
  Text,
  Avatar,
  Chip,
  FAB,
  Surface,
  Divider,
  useTheme
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { DUNBAR_CIRCLES, CONTACT_CATEGORIES } from '../constants/dunbarCircles';
import ContactReminderStatus from '../components/ContactReminderStatus';
import InteractionHistory from '../components/InteractionHistory';

export default function ContactDetailScreen({ route, navigation }) {
  const { contact: contactParam } = route.params;
  const { state } = useApp();
  const theme = useTheme();
  const [contactInteractions, setContactInteractions] = useState([]);

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Get updated contact data from state (in case it was edited)
  // contactParam might be serialized JSON, so we need to get the actual Contact instance from state
  const currentContact = state.contacts.find(c => c.id === contactParam.id) || contactParam;
  const circle = DUNBAR_CIRCLES[currentContact.circle];

  useEffect(() => {
    // Filter interactions for this contact
    const interactions = state.interactions.filter(i => i.contactId === currentContact.id);
    // Sort by date (most recent first)
    const sortedInteractions = interactions.sort((a, b) => new Date(b.date) - new Date(a.date));
    setContactInteractions(sortedInteractions);
  }, [state.interactions, currentContact.id]);

  // Get contact categories for display
  const getContactCategories = () => {
    if (!currentContact.categories || currentContact.categories.length === 0) return [];
    return currentContact.categories.map(categoryKey => CONTACT_CATEGORIES[categoryKey]).filter(Boolean);
  };

  const categories = getContactCategories();

  // Handle log interaction navigation
  const handleLogInteraction = () => {
    navigation.navigate('LogInteraction', { contact: currentContact.toJSON() });
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Contact Details" />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Contact Overview Section */}
        <Card style={styles.overviewCard}>
          <Card.Content>
            <View style={styles.contactHeader}>
              <Avatar.Text
                size={64}
                label={currentContact.name.charAt(0).toUpperCase()}
                style={[styles.avatar, { backgroundColor: circle.color }]}
              />
              <View style={styles.contactInfo}>
                <Text variant="headlineSmall" style={styles.contactName}>
                  {currentContact.name}
                </Text>
                <View style={styles.circleContainer}>
                  <Surface 
                    style={[styles.circleIndicator, { backgroundColor: circle.color }]} 
                    elevation={1}
                  />
                  <Text variant="bodyLarge" style={styles.circleName}>
                    {circle.name}
                  </Text>
                </View>
              </View>
            </View>

            {/* Categories */}
            {categories.length > 0 && (
              <View style={styles.categoriesSection}>
                <Text variant="bodyMedium" style={styles.sectionLabel}>Categories</Text>
                <View style={styles.categoriesContainer}>
                  {categories.map((category, index) => (
                    <Chip
                      key={index}
                      mode="outlined"
                      compact
                      icon={category.icon}
                      style={[styles.categoryChip, { borderColor: category.color }]}
                      textStyle={[styles.categoryText, { color: category.color }]}
                    >
                      {category.name}
                    </Chip>
                  ))}
                </View>
              </View>
            )}

            {/* Contact Information */}
            <View style={styles.contactDetailsSection}>
              {currentContact.phone && (
                <View style={styles.contactDetailRow}>
                  <Text variant="bodyMedium" style={styles.contactDetailLabel}>Phone:</Text>
                  <Text variant="bodyMedium" style={styles.contactDetailValue}>
                    {currentContact.phone}
                  </Text>
                </View>
              )}
              {currentContact.email && (
                <View style={styles.contactDetailRow}>
                  <Text variant="bodyMedium" style={styles.contactDetailLabel}>Email:</Text>
                  <Text variant="bodyMedium" style={styles.contactDetailValue}>
                    {currentContact.email}
                  </Text>
                </View>
              )}
            </View>

            {/* Notes */}
            {currentContact.notes && (
              <View style={styles.notesSection}>
                <Text variant="bodyMedium" style={styles.sectionLabel}>Notes</Text>
                <Text variant="bodyMedium" style={styles.notesText}>
                  {currentContact.notes}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Next Reminder Section */}
        <ContactReminderStatus contact={currentContact} />

        {/* Interaction History Section */}
        <InteractionHistory
          interactions={contactInteractions}
          contactName={currentContact.name}
          onLogInteraction={handleLogInteraction}
          navigation={navigation}
        />
      </ScrollView>

      {/* Floating Action Button for Edit */}
      <FAB
        icon="pencil"
        style={styles.fab}
        onPress={() => navigation.navigate('EditContact', { contact: currentContact.toJSON() })}
      />
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  overviewCard: {
    marginBottom: 16,
    elevation: 2,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    marginRight: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  circleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  circleName: {
    fontWeight: '500',
  },
  categoriesSection: {
    marginBottom: 16,
  },
  sectionLabel: {
    fontWeight: '600',
    marginBottom: 8,
    opacity: 0.8,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    height: 28,
  },
  categoryText: {
    fontSize: 12,
  },
  contactDetailsSection: {
    marginBottom: 16,
  },
  contactDetailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  contactDetailLabel: {
    fontWeight: '600',
    width: 80,
    opacity: 0.8,
  },
  contactDetailValue: {
    flex: 1,
  },
  notesSection: {
    marginTop: 8,
  },
  notesText: {
    lineHeight: 20,
    opacity: 0.8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
