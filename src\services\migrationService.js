/**
 * Migration Service
 * Handles data migrations between schema versions
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import dataService from './dataService';
import snoozeDataService from './snoozeDataService';
import { ReminderInstance } from '../utils/dataModels';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';

const DATA_VERSION_KEY = '@relateful_data_version';
const CURRENT_DATA_VERSION = 2;

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date with days added
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Get the most recent interaction date for a contact
 * @param {string} contactId - Contact ID
 * @param {Array} interactions - Array of interactions
 * @returns {Date|null} Most recent interaction date or null
 */
const getLastInteractionDate = (contactId, interactions) => {
  const contactInteractions = interactions.filter(i => i.contactId === contactId);
  if (contactInteractions.length === 0) return null;
  
  const sortedInteractions = contactInteractions.sort((a, b) => new Date(b.date) - new Date(a.date));
  return new Date(sortedInteractions[0].date);
};

class MigrationService {
  /**
   * Get current data version
   * @returns {number} Current data version
   */
  async getCurrentDataVersion() {
    try {
      const versionJson = await AsyncStorage.getItem(DATA_VERSION_KEY);
      return versionJson ? parseInt(versionJson, 10) : 1; // Default to version 1
    } catch (error) {
      console.error('Error getting data version:', error);
      return 1;
    }
  }

  /**
   * Set data version
   * @param {number} version - Version to set
   */
  async setDataVersion(version) {
    try {
      await AsyncStorage.setItem(DATA_VERSION_KEY, version.toString());
    } catch (error) {
      console.error('Error setting data version:', error);
      throw error;
    }
  }

  /**
   * Check if migration is needed
   * @returns {boolean} True if migration is needed
   */
  async isMigrationNeeded() {
    const currentVersion = await this.getCurrentDataVersion();
    return currentVersion < CURRENT_DATA_VERSION;
  }

  /**
   * Run all necessary migrations
   * @returns {boolean} True if migrations completed successfully
   */
  async runMigrations() {
    try {
      const currentVersion = await this.getCurrentDataVersion();
      console.log(`Current data version: ${currentVersion}, Target version: ${CURRENT_DATA_VERSION}`);

      if (currentVersion >= CURRENT_DATA_VERSION) {
        console.log('No migration needed');
        return true;
      }

      // Run migrations sequentially
      if (currentVersion < 2) {
        await this.migrateToVersion2();
      }

      // Set final version
      await this.setDataVersion(CURRENT_DATA_VERSION);
      console.log(`Migration completed to version ${CURRENT_DATA_VERSION}`);
      return true;
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate from calculated reminders to stored reminder instances (Version 1 -> 2)
   */
  async migrateToVersion2() {
    console.log('Starting migration to version 2: Calculated reminders -> Stored instances');

    try {
      // Get current data
      const [contacts, interactions, snoozeData] = await Promise.all([
        dataService.getContacts(),
        dataService.getInteractions(),
        snoozeDataService.getSnoozeData()
      ]);

      console.log(`Migrating ${contacts.length} contacts with snooze data for ${Object.keys(snoozeData).length} contacts`);

      const reminderInstances = [];

      // Process each contact
      for (const contact of contacts) {
        if (!contact.recurringReminder) {
          console.log(`Skipping contact ${contact.name} - no recurring reminder`);
          continue;
        }

        const instance = this.createReminderInstanceFromContact(contact, interactions, snoozeData);
        if (instance) {
          reminderInstances.push(instance);
          console.log(`Created reminder instance for ${contact.name}, due: ${instance.reminderDate.toISOString()}`);
        }
      }

      // Save all reminder instances
      for (const instance of reminderInstances) {
        await dataService.saveReminderInstance(instance);
      }

      // Clean up legacy snooze data
      await dataService.clearLegacySnoozeData();

      console.log(`Migration to version 2 completed: Created ${reminderInstances.length} reminder instances`);
    } catch (error) {
      console.error('Error in migration to version 2:', error);
      throw error;
    }
  }

  /**
   * Create a reminder instance from a contact using current calculation logic
   * @param {Contact} contact - Contact object
   * @param {Array} interactions - Array of interactions
   * @param {Object} snoozeData - Snooze data object
   * @returns {ReminderInstance|null} Created reminder instance or null
   */
  createReminderInstanceFromContact(contact, interactions, snoozeData) {
    try {
      const circle = DUNBAR_CIRCLES[contact.circle];
      if (!circle) {
        console.warn(`Unknown circle for contact ${contact.name}: ${contact.circle}`);
        return null;
      }

      const frequency = contact.recurringReminder.frequency;
      const lastInteractionDate = getLastInteractionDate(contact.id, interactions);
      const now = new Date();

      // Calculate reminder date using existing logic
      let reminderDate;
      if (lastInteractionDate) {
        reminderDate = addDays(lastInteractionDate, frequency);
      } else if (contact.lastReminderCompleted) {
        reminderDate = addDays(new Date(contact.lastReminderCompleted), frequency);
      } else {
        reminderDate = addDays(new Date(contact.createdAt || now), frequency);
      }

      // Check for snooze data
      const contactSnooze = snoozeData[contact.id];
      const snoozedUntilDate = contactSnooze?.snoozedUntilDate || null;

      // Create reminder instance
      const instance = new ReminderInstance({
        contactId: contact.id,
        reminderDate: reminderDate,
        completedDate: null, // All start as pending
        snoozedUntilDate: snoozedUntilDate,
        reminderType: 'RECURRING',
        reminderRuleId: contact.recurringReminder.id
      });

      return instance;
    } catch (error) {
      console.error(`Error creating reminder instance for contact ${contact.name}:`, error);
      return null;
    }
  }

  /**
   * Backup current data before migration
   * @returns {Object} Backup data object
   */
  async createBackup() {
    try {
      const [contacts, interactions, snoozeData, userPreferences] = await Promise.all([
        dataService.getContacts(),
        dataService.getInteractions(),
        snoozeDataService.getSnoozeData(),
        dataService.getUserPreferences()
      ]);

      const backup = {
        timestamp: new Date().toISOString(),
        version: await this.getCurrentDataVersion(),
        data: {
          contacts: contacts.map(c => c.toJSON()),
          interactions: interactions.map(i => i.toJSON()),
          snoozeData,
          userPreferences
        }
      };

      // Store backup
      await AsyncStorage.setItem('@relateful_migration_backup', JSON.stringify(backup));
      console.log('Migration backup created');
      
      return backup;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Restore from backup
   * @param {Object} backup - Backup data object
   */
  async restoreFromBackup(backup) {
    try {
      console.log('Restoring from backup...');
      
      // Restore contacts
      await AsyncStorage.setItem('@relateful_contacts', JSON.stringify(backup.data.contacts));
      
      // Restore interactions
      await AsyncStorage.setItem('@relateful_interactions', JSON.stringify(backup.data.interactions));
      
      // Restore snooze data
      await AsyncStorage.setItem('@relateful_snooze_data', JSON.stringify(backup.data.snoozeData));
      
      // Restore user preferences
      await AsyncStorage.setItem('@relateful_preferences', JSON.stringify(backup.data.userPreferences));
      
      // Restore version
      await this.setDataVersion(backup.version);
      
      console.log('Backup restoration completed');
    } catch (error) {
      console.error('Error restoring from backup:', error);
      throw error;
    }
  }

  /**
   * Get backup data
   * @returns {Object|null} Backup data or null if no backup exists
   */
  async getBackup() {
    try {
      const backupJson = await AsyncStorage.getItem('@relateful_migration_backup');
      return backupJson ? JSON.parse(backupJson) : null;
    } catch (error) {
      console.error('Error getting backup:', error);
      return null;
    }
  }

  /**
   * Clear backup data
   */
  async clearBackup() {
    try {
      await AsyncStorage.removeItem('@relateful_migration_backup');
      console.log('Migration backup cleared');
    } catch (error) {
      console.error('Error clearing backup:', error);
    }
  }

  /**
   * Validate migration results
   * @returns {Object} Validation results
   */
  async validateMigration() {
    try {
      const [contacts, reminderInstances] = await Promise.all([
        dataService.getContacts(),
        dataService.getReminderInstances()
      ]);

      const contactsWithReminders = contacts.filter(c => c.recurringReminder);
      const pendingInstances = reminderInstances.filter(i => i.state === 'PENDING');

      const validation = {
        success: true,
        contactsWithReminders: contactsWithReminders.length,
        totalInstances: reminderInstances.length,
        pendingInstances: pendingInstances.length,
        completedInstances: reminderInstances.length - pendingInstances.length,
        errors: []
      };

      // Check that each contact with a reminder has at least one instance
      for (const contact of contactsWithReminders) {
        const contactInstances = reminderInstances.filter(i => i.contactId === contact.id);
        if (contactInstances.length === 0) {
          validation.errors.push(`Contact ${contact.name} has no reminder instances`);
          validation.success = false;
        }
      }

      // Check for orphaned instances
      for (const instance of reminderInstances) {
        const contact = contacts.find(c => c.id === instance.contactId);
        if (!contact) {
          validation.errors.push(`Orphaned reminder instance: ${instance.id}`);
          validation.success = false;
        }
      }

      console.log('Migration validation:', validation);
      return validation;
    } catch (error) {
      console.error('Error validating migration:', error);
      return {
        success: false,
        errors: [`Validation failed: ${error.message}`]
      };
    }
  }
}

export default new MigrationService();
